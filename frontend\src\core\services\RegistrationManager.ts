/**
 * RegistrationManager.ts
 *
 * A centralized service for registering events in the governance chat.
 * This service acts as a knowledge repository for the session, recording
 * user and system actions to provide a complete audit trail.
 */

import { useChatStore } from '../../governance/chat/state/ChatStore';

// Event types for type safety
export enum EventType {
  // Intention and template selection
  INTENTION_SELECTED = 'INTENTION_SELECTED',
  TEMPLATE_SELECTED = 'TEMPLATE_SELECTED',

  // Mindmap actions
  MINDMAP_SELECTED = 'MINDMAP_SELECTED',
  MINDSHEET_CREATED = 'MINDSHEET_CREATED',

  // Node actions
  NODE_CREATED = 'NODE_CREATED',      // When a new node is created
  NODE_SELECTED = 'NODE_SELECTED',    // When a node is selected (single click)
  NODE_OPENED = 'NODE_OPENED',        // When a node is opened (double click)
  NODE_CLOSED = 'NODE_CLOSED',        // When a node is closed
  NODE_EDITED = 'NODE_EDITED',        // When a node's content is edited
  NODE_DELETED = 'NODE_DELETED',      // When a node is deleted
  NODE_MOVED = 'NODE_MOVED',          // When a node is moved

  // Agent actions
  AGENT_SELECTED = 'AGENT_SELECTED',  // When an agent is selected in a node
  AGENT_ACTION = 'AGENT_ACTION',      // When an agent performs an action

  // Connection actions
  CONNECTION_CREATED = 'CONNECTION_CREATED',
  CONNECTION_DELETED = 'CONNECTION_DELETED',

  // ChatFork actions
  CHATFORK_SELECTED = 'CHATFORK_SELECTED',  // When ChatFork is selected
  CHATFORK_TEXT_SELECTED = 'CHATFORK_TEXT_SELECTED',  // When text is selected in ChatFork
  CHATFORK_NODE_CREATED = 'CHATFORK_NODE_CREATED',  // When a node is created from ChatFork
  CHATFORK_FORK_CREATED = 'CHATFORK_FORK_CREATED',  // When a fork chat is created from ChatFork

  // Sheet actions
  SHEET_SWITCHED = 'SHEET_SWITCHED',  // When switching between sheets
  SHEET_ACTIVATED = 'SHEET_ACTIVATED',  // When a sheet is activated
  SHEET_DEACTIVATED = 'SHEET_DEACTIVATED',

  // System actions
  SYSTEM_ERROR = 'SYSTEM_ERROR',      // When a system error occurs
  SYSTEM_INFO = 'SYSTEM_INFO',        // For system information messages
  ERROR_OCCURRED = 'ERROR_OCCURRED',  // When a component error is caught by ErrorBoundary

  // LLM interactions
  LLM_REQUEST_SENT = 'LLM_REQUEST_SENT',        // When a request is sent to an LLM
  LLM_RESPONSE_RECEIVED = 'LLM_RESPONSE_RECEIVED',  // When a response is received from an LLM
  INTENT_DETECTED = 'INTENT_DETECTED',          // When an intent is detected
  MODEL_SELECTED = 'MODEL_SELECTED',            // When a model is selected
  DEFAULT_MODEL_CHANGED = 'DEFAULT_MODEL_CHANGED',  // When the default model is changed

  // API interactions
  API_REQUEST_SENT = 'API_REQUEST_SENT',        // When a request is sent to the API
  API_RESPONSE_RECEIVED = 'API_RESPONSE_RECEIVED',  // When a response is received from the API

  // User interactions
  USER_INPUT = 'USER_INPUT',                    // When the user enters text
  USER_ACTION = 'USER_ACTION',                  // When the user performs an action

  // Application events
  APPLICATION_INITIALIZED = 'APPLICATION_INITIALIZED',  // When the application is initialized
  APPLICATION_CLEANUP = 'APPLICATION_CLEANUP',  // When the application is cleaned up
  THEME_CHANGED = 'THEME_CHANGED',  // When the theme is changed
  VIEWPORT_CHANGED = 'VIEWPORT_CHANGED',  // When the viewport size changes

  // Panel events
  PANEL_OPENED = 'PANEL_OPENED',  // When a panel is opened
  PANEL_CLOSED = 'PANEL_CLOSED',  // When a panel is closed
  PANEL_COLLAPSED = 'PANEL_COLLAPSED',  // When a panel is collapsed
  PANEL_EXPANDED = 'PANEL_EXPANDED',  // When a panel is expanded
  PANEL_MOVED = 'PANEL_MOVED',  // When a panel is moved

  // Dialog events
  DIALOG_OPENED = 'DIALOG_OPENED',  // When a dialog is opened
  DIALOG_CLOSED = 'DIALOG_CLOSED',  // When a dialog is closed

  // Loading state events
  LOADING_STATE_CHANGED = 'LOADING_STATE_CHANGED',

  // MindBook events
  MINDBOOK_CREATED = 'MINDBOOK_CREATED',
  MINDBOOK_OPENED = 'MINDBOOK_OPENED',
  MINDBOOK_CLOSED = 'MINDBOOK_CLOSED',
  MINDBOOK_SAVED = 'MINDBOOK_SAVED',

  // Layout events
  LAYOUT_APPLIED = 'LAYOUT_APPLIED',
  LAYOUT_CHANGED = 'LAYOUT_CHANGED',
  VIEW_CENTERED = 'VIEW_CENTERED'
}

// Data interface for event registration
export interface EventData {
  id?: string;
  name?: string;
  type?: string;
  details?: any;
  [key: string]: any; // Allow for additional properties
}

// Define which events are considered "important" for recording
const IMPORTANT_EVENTS = new Set([
  // Mindsheet and mindmap creation
  EventType.MINDSHEET_CREATED,
  EventType.MINDMAP_SELECTED,
  
  // Node creation and opening (important interactions)
  EventType.NODE_CREATED,
  EventType.NODE_OPENED,
  
  // ChatFork interactions
  EventType.CHATFORK_SELECTED,
  EventType.CHATFORK_NODE_CREATED,
  EventType.CHATFORK_FORK_CREATED,
  
  // Intention and template selection
  EventType.INTENTION_SELECTED,
  EventType.TEMPLATE_SELECTED,
  
  // Sheet management
  EventType.SHEET_ACTIVATED,
  EventType.SHEET_SWITCHED,
  
  // CRITICAL: MindBook events (needed for session loading and "open mindbook")
  EventType.MINDBOOK_CREATED,
  EventType.MINDBOOK_OPENED,
  EventType.MINDBOOK_CLOSED,
  EventType.MINDBOOK_SAVED,
  
  // CRITICAL: Application events (needed for app initialization)
  EventType.APPLICATION_INITIALIZED,
  
  // Important system events
  EventType.SYSTEM_ERROR,
  EventType.ERROR_OCCURRED,
  
  // LLM interactions (user-initiated)
  EventType.LLM_REQUEST_SENT,
  EventType.INTENT_DETECTED,
  
  // User inputs and actions
  EventType.USER_INPUT,
  EventType.USER_ACTION
]);

// Define which events should be excluded (unimportant movements and UI interactions)
const EXCLUDED_EVENTS = new Set([
  // Node and panel movements (not important)
  EventType.NODE_MOVED,
  EventType.PANEL_MOVED,
  
  // Simple selections (too verbose)
  EventType.NODE_SELECTED,
  
  // UI state changes (not important for recording)
  EventType.PANEL_OPENED,
  EventType.PANEL_CLOSED,
  EventType.PANEL_COLLAPSED,
  EventType.PANEL_EXPANDED,
  
  // Theme and viewport changes (UI-only)
  EventType.THEME_CHANGED,
  EventType.VIEWPORT_CHANGED,
  
  // Layout changes (automatic)
  EventType.LAYOUT_APPLIED,
  EventType.LAYOUT_CHANGED,
  EventType.VIEW_CENTERED,
  
  // Loading states (too verbose)
  EventType.LOADING_STATE_CHANGED
]);

/**
 * RegistrationManager class
 *
 * Singleton class for registering events in the governance chat.
 */
class RegistrationManager {
  private static instance: RegistrationManager;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the RegistrationManager
   */
  public static getInstance(): RegistrationManager {
    if (!RegistrationManager.instance) {
      RegistrationManager.instance = new RegistrationManager();
    }
    return RegistrationManager.instance;
  }

  /**
   * Register an event in the governance chat
   * Now includes filtering to only record important events
   *
   * @param eventType The type of event
   * @param data Additional data about the event
   */
  public registerEvent(eventType: EventType, data: EventData): void {
    // Check if this event should be filtered out
    if (EXCLUDED_EVENTS.has(eventType)) {
      console.log(`[RegistrationManager] Filtered out unimportant event: ${eventType}`, data);
      return;
    }

    // Check if this event is explicitly important
    if (!IMPORTANT_EVENTS.has(eventType)) {
      console.log(`[RegistrationManager] Filtered out non-important event: ${eventType}`, data);
      return;
    }

    // Format the event message without timestamp
    let messageText = this.formatEventMessage(eventType, data);
    const timestamp = new Date();

    // Create a system message for display in the govbox
    const systemMessage = {
      id: timestamp.getTime().toString(),
      text: messageText,
      sender: 'system',
      timestamp: timestamp
    };

    // Add the message to the chat store to display in the govbox
    useChatStore.getState().addMessage(systemMessage);

    // Also log to the backend logging service
    this.sendLogToBackend(eventType, messageText, data, timestamp);

    // Log to console for debugging
    console.log(`[RegistrationManager] Important event registered: ${eventType}`, data);
  }

  /**
   * Register an event without filtering (for system-critical events)
   * Use this for events that must always be recorded regardless of filtering
   */
  public registerCriticalEvent(eventType: EventType, data: EventData): void {
    // Format the event message without timestamp
    let messageText = this.formatEventMessage(eventType, data);
    const timestamp = new Date();

    // Create a system message for display in the govbox
    const systemMessage = {
      id: timestamp.getTime().toString(),
      text: messageText,
      sender: 'system',
      timestamp: timestamp
    };

    // Add the message to the chat store to display in the govbox
    useChatStore.getState().addMessage(systemMessage);

    // Also log to the backend logging service
    this.sendLogToBackend(eventType, messageText, data, timestamp);

    // Log to console for debugging
    console.log(`[RegistrationManager] Critical event registered: ${eventType}`, data);
  }

  /**
   * Send a log entry to the backend logging service
   *
   * @param eventType The type of event
   * @param message The formatted message
   * @param data Additional data about the event
   * @param timestamp The timestamp of the event
   */
  private sendLogToBackend(eventType: EventType, message: string, data: EventData, timestamp: Date): void {
    try {
      // Create the log entry
      const logEntry = {
        timestamp: timestamp.toISOString(),
        event_type: eventType,
        source: 'frontend',
        details: data,
        message: message
      };

      // Send to backend logging service
      // Only attempt to send logs if we're not in development mode with mock data
      if (window.location.hostname !== 'localhost' || window.location.port !== '5173') {
        fetch('/api/logging/event', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(logEntry)
        })
        .catch(error => {
          // Silently fail if the backend is not available
          console.error('Failed to send log to backend:', error);
        });
      } else {
        // In development mode, just log to console
        console.log('DEV MODE: Not sending log to backend:', logEntry);
      }
    } catch (error) {
      // Ignore errors to prevent disrupting the application
      console.error('Error sending log to backend:', error);
    }
  }

  /**
   * Format event message based on event type and data
   */
  private formatEventMessage(eventType: EventType, data: EventData): string {
    switch (eventType) {
      // Intention and template selection
      case EventType.INTENTION_SELECTED:
        return `Selected intention: ${data.name || 'Unknown'}`;

      case EventType.TEMPLATE_SELECTED:
        return `Selected template: ${data.name || 'Unknown'}`;

      // Mindmap actions
      case EventType.MINDMAP_SELECTED:
        return 'Mindmap creation selected';

      case EventType.MINDSHEET_CREATED:
        const sheetType = data.type || 'sheet';
        const title = data.title || data.name || data.id || 'Unknown';
        return `Created ${sheetType}: ${title}`;

      // Node actions
      case EventType.NODE_CREATED:
        return `Created node: ${data.text || data.title || data.id || 'Untitled'}`;

      case EventType.NODE_OPENED:
        return `Opened node: ${data.text || data.title || data.id || 'Unknown'}`;

      case EventType.NODE_CLOSED:
        return `Closed node: ${data.text || data.title || data.id || 'Unknown'}`;

      case EventType.NODE_EDITED:
        return `Edited node: ${data.text || data.title || data.id || 'Unknown'}`;

      case EventType.NODE_DELETED:
        return `Deleted node: ${data.text || data.title || data.id || 'Unknown'}`;

      // Agent actions
      case EventType.AGENT_SELECTED:
        return `Selected agent: ${data.agent || 'Unknown'} for node: ${data.nodeId || 'Unknown'}`;

      case EventType.AGENT_ACTION:
        return `Agent action: ${data.action || 'Unknown'} by ${data.agent || 'Unknown'}`;

      // Connection actions
      case EventType.CONNECTION_CREATED:
        return `Connected nodes: ${data.fromId || 'Unknown'} → ${data.toId || 'Unknown'}`;

      case EventType.CONNECTION_DELETED:
        return `Disconnected nodes: ${data.fromId || 'Unknown'} ↛ ${data.toId || 'Unknown'}`;

      // ChatFork actions
      case EventType.CHATFORK_SELECTED:
        return 'ChatFork selected for exploration';

      case EventType.CHATFORK_TEXT_SELECTED:
        const selectedText = data.text || 'text';
        const preview = selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText;
        return `Selected text in ChatFork: "${preview}"`;

      case EventType.CHATFORK_NODE_CREATED:
        return `Created ChatFork node: ${data.text || data.title || 'Untitled'}`;

      case EventType.CHATFORK_FORK_CREATED:
        const forkTopic = data.topic || data.text || data.title || 'topic';
        const action = data.action || 'created';
        if (action === 'removed') {
          return `Removed fork chat: ${forkTopic}`;
        } else {
          return `Created fork chat: ${forkTopic}`;
        }

      // Sheet actions
      case EventType.SHEET_SWITCHED:
        return `Switched to sheet: ${data.sheetId || data.name || 'Unknown'}`;

      case EventType.SHEET_ACTIVATED:
        return `Activated sheet: ${data.sheetId || data.name || 'Unknown'}`;

      case EventType.SHEET_DEACTIVATED:
        return `Deactivated sheet: ${data.sheetId || data.name || 'Unknown'}`;

      // MindBook session management events
      case EventType.MINDBOOK_OPENED:
        if (data.type === 'auto_restore') {
          return `Welcome back! Restored session with ${data.sheets || 0} sheets`;
        } else if (data.type === 'user_load') {
          return `Welcome back to "${data.name}"! Last saved: ${data.lastSaved || 'Unknown'}`;
        } else {
          return `Opened MindBook: ${data.name || 'Unnamed'}`;
        }

      case EventType.MINDBOOK_CLOSED:
        return `User closed MindBook session: "${data.name}" (${data.sheets || 0} sheets)`;

      case EventType.MINDBOOK_SAVED:
        const saveDesc = data.description ? ` - ${data.description}` : '';
        return `Saved MindBook: "${data.name}" (${data.sheets || 0} sheets)${saveDesc}`;

      case EventType.MINDBOOK_CREATED:
        return `Created new MindBook: ${data.name || 'Unnamed'}`;

      // System events
      case EventType.SYSTEM_ERROR:
        return `System error: ${data.message || data.error || 'Unknown error'}`;

      case EventType.SYSTEM_INFO:
        return `System info: ${data.message || data.info || 'No details'}`;

      case EventType.ERROR_OCCURRED:
        return `Error occurred: ${data.message || data.error || 'Unknown error'}`;

      // LLM interactions
      case EventType.LLM_REQUEST_SENT:
        return `Sent query to ${data.model || 'AI'}: "${data.query || data.message || 'Query'}"`;

      case EventType.LLM_RESPONSE_RECEIVED:
        return `Received response from ${data.model || 'AI'}`;

      case EventType.INTENT_DETECTED:
        return `Detected intent: ${data.intent || 'Unknown'}`;

      case EventType.MODEL_SELECTED:
        return `Selected model: ${data.model || 'Unknown'}`;

      case EventType.DEFAULT_MODEL_CHANGED:
        return `Changed default model to: ${data.model || 'Unknown'}`;

      // API interactions
      case EventType.API_REQUEST_SENT:
        return `API request sent to: ${data.endpoint || 'Unknown endpoint'}`;

      case EventType.API_RESPONSE_RECEIVED:
        return `API response received from: ${data.endpoint || 'Unknown endpoint'}`;

      // User interactions
      case EventType.USER_INPUT:
        const inputText = data.text || data.message || data.input || '';
        const inputPreview = inputText.length > 100 ? inputText.substring(0, 100) + '...' : inputText;
        return `User input: "${inputPreview}"`;

      case EventType.USER_ACTION:
        return `User action: ${data.action || data.type || 'Unknown action'}`;

      // Application events
      case EventType.APPLICATION_INITIALIZED:
        return 'Application initialized successfully';

      case EventType.APPLICATION_CLEANUP:
        return 'Application cleanup completed';

      // Default case
      default:
        return `${eventType}: ${JSON.stringify(data)}`;
    }
  }

  /**
   * Get a list of important event types for filtering
   */
  public static getImportantEvents(): Set<EventType> {
    return new Set(IMPORTANT_EVENTS);
  }

  /**
   * Get a list of excluded event types
   */
  public static getExcludedEvents(): Set<EventType> {
    return new Set(EXCLUDED_EVENTS);
  }

  /**
   * Check if an event type is considered important
   */
  public static isImportantEvent(eventType: EventType): boolean {
    return IMPORTANT_EVENTS.has(eventType) && !EXCLUDED_EVENTS.has(eventType);
  }
}

// Export the singleton instance for use throughout the application
export default RegistrationManager.getInstance();
