// Placeholder file for: frontend\src\components\MindMap\services\api\LLMService.ts

export interface LLMConfig {
  model?: string;
  useMockResponses?: boolean;
  apiKey?: string;
  apiEndpoint?: string;
  temperature?: number;
  maxTokens?: number;
}

export abstract class LLMService {
  protected config: LLMConfig;
  protected errorCounter: number = 0;

  protected constructor(config: LLMConfig = {}) {
    // Get the API base URL from the global config if available
    const defaultApiEndpoint = (window as any).API_BASE_URL || 'http://localhost:8000/api';
    
    this.config = {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1000,
      useMockResponses: false,
      // Use the backend API endpoint by default, not the OpenAI API
      apiEndpoint: defaultApiEndpoint,
      ...config
    };
    
    console.log('[LLMService] Initialized with API endpoint:', this.config.apiEndpoint);
  }

  protected async makeAPICall(prompt: string, systemPrompt?: string, promptType: string = 'default'): Promise<any> {
    console.log('[LLMService] Starting API call with prompt:', prompt.substring(0, 100) + '...');
    
    if (this.config.useMockResponses) {
      console.log("[LLMService] Using mock response instead of API call");
      return this.generateMockResponse(prompt, systemPrompt);
    }

    try {
      // Get the API endpoint from config
      const apiBaseUrl = this.config.apiEndpoint || 'http://localhost:8000/api';
      
      // Construct the correct URL for our backend API
      const apiUrl = `${apiBaseUrl}/llm/chat`;
      
      console.log('[LLMService] Using API URL:', apiUrl);

      // Format messages according to our backend API requirements
      const payload = {
        prompt,
        system_prompt: systemPrompt,
        prompt_type: promptType,
        model: this.config.model,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      };

      console.log('[LLMService] Sending request with payload:', payload);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors', // Add CORS mode for cross-origin requests
        body: JSON.stringify(payload)
      });

      console.log('[LLMService] Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[LLMService] API error response:', errorText);
        throw new Error(`API call failed with status ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('[LLMService] Raw API response:', data);
      
      // If we're getting a direct response from the backend
      if (data.success && data.content) {
        return data;
      }
      
      // Handle error responses from the backend
      if (!data.success && data.error) {
        console.error('[LLMService] Backend API error:', data.error);
        throw new Error(data.error || 'Unknown error from backend');
      }
      
      // Return the data as-is if it doesn't match expected formats
      return data;
    } catch (error) {
      console.error('[LLMService] Error during API call:', error);
      this.errorCounter++;
      
      // For now, fall back to mock response on error
      console.log('[LLMService] Falling back to mock response due to error');
      return this.generateMockResponse(prompt, systemPrompt);
    }
  }

  protected validateResponse(response: any): boolean {
    return response?.choices?.[0]?.message?.content !== undefined;
  }

  protected handleError(error: Error): never {
    console.error('LLM Service Error:', error);
    throw error;
  }

  protected extractContent(response: any): string {
    console.log('[LLMService] Extracting content from response:', JSON.stringify(response).substring(0, 200) + '...');
    
    // Handle different response formats
    if (response.success === true && response.content) {
      // Backend API format
      console.log('[LLMService] Detected backend API response format');
      return response.content;
    } else if (response.choices && response.choices.length > 0) {
      // OpenAI API format
      console.log('[LLMService] Detected OpenAI API response format');
      return response.choices[0].message.content;
    } else if (typeof response === 'string') {
      // Direct string response
      console.log('[LLMService] Detected direct string response');
      return response;
    } else if (response.message && typeof response.message === 'string') {
      // Simple message object
      console.log('[LLMService] Detected message object response');
      return response.message;
    } else if (response.text && typeof response.text === 'string') {
      // Text property
      console.log('[LLMService] Detected text property response');
      return response.text;
    } else if (response.content && typeof response.content === 'string') {
      // Content property
      console.log('[LLMService] Detected content property response');
      return response.content;
    }
    
    // If we can't determine the format, try to stringify the entire response
    console.log('[LLMService] Unknown response format, attempting to stringify');
    try {
      if (typeof response === 'object') {
        return JSON.stringify(response);
      }
      return String(response);
    } catch (error) {
      console.error('[LLMService] Failed to extract content from response:', error);
      return '';
    }
  }

  protected abstract generateMockResponse(prompt: string, systemPrompt?: string): any;
}
