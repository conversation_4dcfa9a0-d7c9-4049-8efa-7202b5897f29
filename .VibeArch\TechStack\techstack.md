# Technology Stack Analysis

**Last Updated:** 2025-06-05 11:33:13  
**Project:** MindBack_V1  
**Project Path:** C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1

---

## 📋 Summary

This document provides a comprehensive analysis of the technology stack used in this project. The information below was automatically detected by scanning project files, dependencies, and configuration.

## 💻 Programming Languages

- **JavaScript**
- **Python**
- **TypeScript**

## 🎨 Frontend Frameworks & Libraries

- **React**
- **Vite**

## ⚙️ Backend Frameworks & Libraries

- **BeautifulSoup**
- **FastAPI**
- **NumPy**
- **OpenAI**
- **Pandas**
- **Requests**
- **Uvicorn**

## 🔧 Build Tools & Development

- **TypeScript**
- **Vite**
- **npm**
- **pip**

## 🤖 Automation & DevOps Tools

- **AutoHotkey**
- **PowerShell**

## 🗄️ Databases & Data Storage

- **Redis**

## 🔌 Other Technologies

- **AsyncIO**
- **Celery**
- **Markdown Documentation**
- **Mat<PERSON>lotlib**
- **Pytest Testing**
- **Seaborn**
- **Zustand**

## 📁 Project Structure Analysis

### Frontend Directories
- `frontend/`
- `src/`

### Backend Directories
- `backend/`

---

## 📝 Notes

This analysis was automatically generated by Vibe Architect's techstack detection system. The information above reflects the current state of the project based on:

- **Package files**: `package.json`, `requirements.txt`, `Gemfile`, etc.
- **Configuration files**: Build tools, framework configs, environment files
- **Project structure**: Directory names and file extensions
- **Dependency analysis**: Direct and development dependencies

For manual customization of this techstack information, use the Vibe Architect interface or edit the `techstack.yaml` configuration file.

*Generated by Vibe Architect Techstack Analyzer*
