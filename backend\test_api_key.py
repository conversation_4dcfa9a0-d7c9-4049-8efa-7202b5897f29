import os
import sys
from dotenv import load_dotenv
from pathlib import Path

# Print debug info
print(f"Python version: {sys.version}")
print(f"Current working directory: {os.getcwd()}")

# Try loading from different possible .env locations
root_dir = Path(__file__).resolve().parent.parent
env_paths = [
    root_dir / '.env',
    Path('.env'),
    Path('../.env'),
]

for path in env_paths:
    print(f"\nTrying to load .env from: {path.absolute()} (exists: {path.exists()})")
    if path.exists():
        try:
            load_dotenv(dotenv_path=path)
            print(f"Loaded .env from {path.absolute()}")
            break
        except Exception as e:
            print(f"Error loading from {path}: {e}")

# Check the API key
api_key = os.getenv("OPENAI_API_KEY")
if api_key:
    print(f"API key found with length: {len(api_key)}")
    print(f"First 4 characters: {api_key[:4]}")
    print(f"Last 4 characters: {api_key[-4:]}")
    if "your_" in api_key:
        print("WARNING: API key contains 'your_', which suggests it's a placeholder")
else:
    print("WARNING: No OpenAI API key found")

# Try initializing the OpenAI client
print("\nTrying to initialize OpenAI client directly:")
try:
    import openai
    openai.api_key = api_key
    print("OpenAI client initialized successfully")
except Exception as e:
    print(f"Error initializing OpenAI client: {e}")

# Try LangChain OpenAI client
print("\nTrying to initialize LangChain OpenAI client:")
try:
    from langchain_openai import ChatOpenAI
    llm = ChatOpenAI(
        model="gpt-3.5-turbo",
        temperature=0.7,
        api_key=api_key
    )
    print("LangChain OpenAI client initialized successfully")
except Exception as e:
    print(f"Error initializing LangChain OpenAI client: {e}")

# Try CrewAI
print("\nTrying to initialize CrewAI:")
try:
    from crewai import Agent
    agent = Agent(
        role="Tester",
        goal="Test API key",
        backstory="I'm testing the API key",
        verbose=True,
        allow_delegation=False,
        llm=ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.7,
            api_key=api_key
        )
    )
    print("CrewAI Agent initialized successfully")
except Exception as e:
    print(f"Error initializing CrewAI: {e}")

print("\nTest complete.") 