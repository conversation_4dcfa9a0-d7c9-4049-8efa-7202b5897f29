"""
OpenAI service for MindBack application.
This module provides utilities for interacting with the OpenAI API.
"""
import os
import logging
import json
from typing import Any, Dict, List, Optional, Union
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, validator
from ..config.settings import get_settings
from fastapi import HTTPException

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

# Response structure models
class MetadataModel(BaseModel):
    intent: Optional[str] = Field(None, description="Node intent classification")
    agent: Optional[str] = Field(None, description="Thinking hat perspective")
    tags: Optional[List[str]] = Field(default_factory=list, description="Node tags")
    action: Optional[Dict[str, Any]] = Field(None, description="Action item details")

    @validator('intent')
    def validate_intent(cls, v):
        if v and v not in ["factual", "exploratory", "teleological", "instantiation", "miscellaneous"]:
            raise ValueError(f"Invalid intent value: {v}")
        return v

    @validator('agent')
    def validate_agent(cls, v):
        if v and v not in ["blue_hat", "white_hat", "red_hat", "black_hat", "yellow_hat", "green_hat"]:
            raise ValueError(f"Invalid agent value: {v}")
        return v

class NodeModel(BaseModel):
    id: str = Field(..., description="Unique node identifier")
    text: str = Field(..., description="Node title text")
    description: str = Field(..., description="Node detailed description")
    children: List['NodeModel'] = Field(default_factory=list, description="Child nodes")
    metadata: MetadataModel = Field(default_factory=MetadataModel, description="Node metadata")

NodeModel.update_forward_refs()

def get_openai_client() -> AsyncOpenAI:
    """
    Get an authenticated OpenAI client using API key from environment variables.
    """
    settings = get_settings()
    
    api_key = settings.openai_api_key
    if not api_key:
        api_key = os.environ.get("OPENAI_API_KEY")
        
    if not api_key:
        logger.error("OpenAI API key not found in environment variables")
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    return AsyncOpenAI(api_key=api_key)

async def validate_and_transform_response(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and transform the OpenAI response to ensure it matches our required format
    """
    try:
        # Extract function call if present
        if (
            'choices' in response_data 
            and len(response_data['choices']) > 0 
            and 'message' in response_data['choices'][0]
            and 'function_call' in response_data['choices'][0]['message']
        ):
            function_data = json.loads(response_data['choices'][0]['message']['function_call']['arguments'])
            
            # Log the extracted function data
            logger.info("Validating function response data structure")
            logger.debug(f"Function data: {json.dumps(function_data, indent=2)}")
            
            # Validate the structure matches our models
            if 'mindmap' in function_data:
                root_data = function_data['mindmap']['root']
                # Create NodeModel instance to validate structure
                root_node = NodeModel(
                    id=root_data.get('id', f"root-{hash(root_data['text'])}"),
                    text=root_data['text'],
                    description=root_data['description'],
                    children=root_data.get('children', []),
                    metadata=MetadataModel(
                        intent=root_data.get('intent'),
                        agent=root_data.get('agent'),
                        tags=root_data.get('tags', []),
                        action=root_data.get('action')
                    )
                )
                
                # Convert back to dict with validated structure
                validated_data = root_node.model_dump()
                logger.info("Successfully validated and transformed response data")
                
                return {
                    'mindmap': {
                        'root': validated_data
                    }
                }
            
            return function_data
            
        return response_data
        
    except Exception as e:
        logger.error(f"Error validating response data: {str(e)}")
        raise ValueError(f"Invalid response data structure: {str(e)}")

async def generate_openai_chat_completion(
    client: AsyncOpenAI,
    messages: List[Dict[str, str]],
    model: str = "gpt-3.5-turbo-0125",
    temperature: float = 0.7,
    max_tokens: Optional[int] = None,
    functions: Optional[List[Dict[str, Any]]] = None,
    function_call: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """
    Generate an OpenAI chat completion with enhanced error handling and response validation
    """
    try:
        # Log the request
        logger.info(f"Sending request to OpenAI API with model {model}")
        logger.debug(f"Messages: {messages[:2]}...")  # Log first two messages
        
        # Prepare request parameters
        params = {
            "model": model,
            "messages": messages,
            "temperature": temperature
        }
        
        # Add function calling if provided
        if functions:
            params["functions"] = functions
            logger.info("Function calling enabled with schema")
            logger.debug(f"Functions schema: {json.dumps(functions, indent=2)}")
            
        if function_call:
            params["function_call"] = function_call
        
        # Call the API
        completion = await client.chat.completions.create(**params)
        
        # Convert to dict for easier handling
        response_dict = completion.model_dump()
        
        # Log the raw response for debugging
        logger.info("=== FULL RAW OPENAI RESPONSE ===")
        logger.info(json.dumps(response_dict, indent=2))
        logger.info("================================")
        
        # Validate and transform the response
        try:
            validated_response = await validate_and_transform_response(response_dict)
            logger.info("Response validation successful")
            
            # Add validation metadata
            response_dict['validated'] = True
            response_dict['transformed_content'] = validated_response
            
            return response_dict
            
        except ValueError as ve:
            logger.warning(f"Response validation failed: {str(ve)}")
            # Still return the response but mark as not validated
            response_dict['validated'] = False
            response_dict['validation_error'] = str(ve)
            return response_dict
        
    except Exception as e:
        logger.error(f"Error in OpenAI API call: {str(e)}")
        if hasattr(e, 'response') and hasattr(e.response, 'json'):
            try:
                error_details = e.response.json()
                logger.error(f"API error details: {error_details}")
            except:
                pass
        raise HTTPException(
            status_code=500,
            detail={
                "error": "OpenAI API error",
                "message": str(e),
                "type": type(e).__name__
            }
        ) 