"""
Configuration settings for the MindBack API.
Handles loading settings from environment variables.
"""

from pydantic_settings import BaseSettings
import os
from typing import Optional
from functools import lru_cache
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    """
    Application settings loaded from environment variables
    """
    # API keys
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra fields in .env file

    # API settings
    enable_mock_responses: bool = os.getenv("ENABLE_MOCK_RESPONSES", "False").lower() == "true"
    debug_mode: bool = os.getenv("DEBUG_MODE", "False").lower() == "true"

    # Path configurations
    prompt_library_path: str = os.getenv(
        "PROMPT_LIBRARY_PATH",
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Prompt_library")
    )

    # Model configurations
    default_model: str = os.getenv("DEFAULT_MODEL", "gpt-3.5-turbo")
    default_temperature: float = float(os.getenv("DEFAULT_TEMPERATURE", "0.7"))

    # Config class moved up to avoid duplicate

@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance
    """
    return Settings()