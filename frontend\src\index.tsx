// Filter out React Router v7 future flag warnings and other development messages
const originalConsoleWarn = console.warn;
console.warn = function filterWarnings(msg, ...args) {
  // Skip development-related warnings that don't affect functionality
  if (typeof msg === 'string' && (
    // React Router warnings
    msg.includes('React Router Future Flag Warning') ||
    // React DevTools suggestion
    msg.includes('Download the React DevTools')
  )) {
    return; // Suppress the warning
  }
  // Otherwise, pass through to the original console.warn
  return originalConsoleWarn.apply(console, [msg, ...args]);
};

// Enable mock responses by default to avoid API errors
try {
  // Only set if not already set
  if (!localStorage.getItem('useMockResponses')) {
    localStorage.setItem('useMockResponses', 'true');
    console.log('Mock responses enabled by default');
  }
} catch (error) {
  console.error('Failed to set mock responses:', error);
}

// Define the API base URL
const API_BASE_URL = 'http://localhost:8000/api';

// Make it globally available
(window as any).API_BASE_URL = API_BASE_URL;

// Test backend connectivity during startup
async function checkBackendConnectivity() {
  try {
    console.log('Checking backend API connectivity...');
    // Use the same API base URL for consistency
    const response = await fetch(`${API_BASE_URL}/llm/health`, {
      // Add mode: 'cors' to handle cross-origin requests
      mode: 'cors'
    });
    console.log('Health check response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend API is available:', data);

      // Optionally disable mock mode if not explicitly set by user
      if (!localStorage.getItem('userSetMockMode')) {
        // Update both flags for consistency
        const previousLiveSetting = localStorage.getItem('use_live_llm');
        const previousMockSetting = localStorage.getItem('useMockResponses');

        // Enable live LLM and disable mock responses
        localStorage.setItem('use_live_llm', 'true');
        localStorage.setItem('useMockResponses', 'false');

        console.log('Backend available - updating LLM settings:', {
          previousLiveSetting,
          newLiveSetting: 'true',
          previousMockSetting,
          newMockSetting: 'false'
        });
      }
    } else {
      console.warn('⚠️ Backend API health check failed with status:', response.status);

      // Update both flags for consistency
      const previousLiveSetting = localStorage.getItem('use_live_llm');
      const previousMockSetting = localStorage.getItem('useMockResponses');

      // Disable live LLM and enable mock responses
      localStorage.setItem('use_live_llm', 'false');
      localStorage.setItem('useMockResponses', 'true');

      console.log('Backend unavailable - forcing mock mode:', {
        previousLiveSetting,
        newLiveSetting: 'false',
        previousMockSetting,
        newMockSetting: 'true'
      });
    }
  } catch (error) {
    console.error('❌ Backend API connectivity check failed:', error);

    // Update both flags for consistency
    const previousLiveSetting = localStorage.getItem('use_live_llm');
    const previousMockSetting = localStorage.getItem('useMockResponses');

    // Disable live LLM and enable mock responses
    localStorage.setItem('use_live_llm', 'false');
    localStorage.setItem('useMockResponses', 'true');

    console.log('Backend connection error - forcing mock mode:', {
      previousLiveSetting,
      newLiveSetting: 'false',
      previousMockSetting,
      newMockSetting: 'true'
    });
  }
}

// Run the connectivity check asynchronously
checkBackendConnectivity().catch(error => {
  console.error('Error during API connectivity check:', error);
});

// Import React and ReactDOM
import React from 'react';
import ReactDOM from 'react-dom/client';

// Import our refactored App component
import AppRefactored from './AppRefactored';
import './index.css';

// Import React compatibility layer
import { initReactCompatibility } from './utils/reactCompatibility';

// Initialize React compatibility before rendering
initReactCompatibility();

// Render the refactored App component
const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(<AppRefactored />);
