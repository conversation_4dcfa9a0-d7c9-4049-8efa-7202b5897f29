/**
 * MemorySnapshotService.ts
 *
 * Collects application memory and localStorage data and sends it to the backend.
 */

import { useMindBookStore } from '../state/MindBookStore';
import { useChatStore } from '../../governance/chat/state/ChatStore';
import { useContextStore } from '../../features/context/store/ContextStore';
import { ChatMemoryService } from '../../services/ChatMemoryService';

interface MemorySnapshot {
  timestamp: string;
  mindBook: any;
  contextSettings: any;
  chatMessages: any;
  chatMemory: any;
  localStorage: Record<string, any>;
  logs: any;
}

class MemorySnapshotService {
  private static filterKeys = [
    'mindbook_',
    'mindmap_sheet_',
    'context_settings_',
    'context_settings_list',
    'current_context_settings',
    'mindbook_autosave'
  ];

  static collectSnapshot = async (): Promise<MemorySnapshot> => {
    const mindBook = useMindBookStore.getState();
    const chatStore = useChatStore.getState();
    const contextStore = useContextStore.getState();
    const chatMemory = ChatMemoryService.getInstance().getCurrentContext();

    const storage: Record<string, any> = {};
    Object.keys(localStorage).forEach(key => {
      if (this.filterKeys.some(prefix => key.startsWith(prefix))) {
        try {
          storage[key] = JSON.parse(localStorage.getItem(key) as string);
        } catch {
          storage[key] = localStorage.getItem(key);
        }
      }
    });

    // Capture logs from backend if available
    let logs: any = null;
    try {
      const resp = await fetch('/api/logging/events');
      if (resp.ok) {
        logs = await resp.json();
      }
    } catch {
      // Ignore errors when fetching logs
    }

    return {
      timestamp: new Date().toISOString(),
      mindBook,
      contextSettings: contextStore.currentContextSettings,
      chatMessages: chatStore.messages,
      chatMemory,
      localStorage: storage,
      logs
    };
  };

  static exportSnapshot = async (): Promise<boolean> => {
    try {
      const snapshot = await this.collectSnapshot();
      const resp = await fetch('/api/memory/snapshot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(snapshot)
      });
      return resp.ok;
    } catch {
      return false;
    }
  };
}

export default MemorySnapshotService;
export const collectMemorySnapshot = () => MemorySnapshotService.collectSnapshot();
export const exportMemorySnapshot = () => MemorySnapshotService.exportSnapshot();
