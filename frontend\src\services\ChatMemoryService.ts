import { MessageStatus } from '../types/MessageStatus'; // Correct relative path
import { ResponseType, SuggestedAction } from './api/GovernanceLLM'; // This path is correct

export interface StructuredMessage {
  id: string;
  content: string;
  sender: 'user' | 'llm';
  timestamp: Date;
  parentId?: string;
  tags?: string[];
  status?: MessageStatus;
  responseType?: ResponseType;
  suggestedActions?: SuggestedAction[];
}

export interface ChatContext {
  recentMessages: StructuredMessage[];
  relevantNodes: string[];
  activeHat?: string;
}

export class ChatMemoryService {
  private static instance: ChatMemoryService;
  private currentContext: ChatContext;
  private readonly maxRecentMessages: number = 10;

  private constructor() {
    this.currentContext = {
      recentMessages: [],
      relevantNodes: [],
    };
  }

  static getInstance(): ChatMemoryService {
    if (!ChatMemoryService.instance) {
      ChatMemoryService.instance = new ChatMemoryService();
    }
    return ChatMemoryService.instance;
  }

  getCurrentContext(): ChatContext {
    return this.currentContext;
  }

  clearMemory(): void {
    this.currentContext = {
      recentMessages: [],
      relevantNodes: [],
    };
  }

  addStructuredMessage(
    content: string,
    sender: 'user' | 'llm',
    responseType?: ResponseType,
    suggestedActions?: SuggestedAction[],
    parentId?: string,
    tags?: string[],
    status?: MessageStatus
  ): void {
    const message: StructuredMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      sender,
      timestamp: new Date(),
      parentId,
      tags,
      status,
      responseType,
      suggestedActions
    };

    this.currentContext.recentMessages.push(message);
    
    if (this.currentContext.recentMessages.length > this.maxRecentMessages) {
      this.currentContext.recentMessages.shift();
    }
  }

  // Legacy method for backward compatibility
  addMessage(
    content: string,
    sender: 'user' | 'llm',
    parentId?: string,
    tags?: string[],
    status?: MessageStatus
  ): void {
    this.addStructuredMessage(content, sender, undefined, undefined, parentId, tags, status);
  }

  setActiveHat(hat: string | undefined): void {
    this.currentContext.activeHat = hat;
  }

  addRelevantNode(nodeId: string): void {
    if (!this.currentContext.relevantNodes.includes(nodeId)) {
      this.currentContext.relevantNodes.push(nodeId);
    }
  }

  removeRelevantNode(nodeId: string): void {
    this.currentContext.relevantNodes = this.currentContext.relevantNodes.filter(
      id => id !== nodeId
    );
  }

  getRelevantMessages(hat?: string): StructuredMessage[] {
    let relevantMessages = [...this.currentContext.recentMessages];

    if (hat) {
      relevantMessages = relevantMessages.filter(msg =>
        msg.tags?.includes(hat) || !msg.tags?.length
      );
    }

    return relevantMessages;
  }

  getMessagesByResponseType(type: 'factual' | 'strategic' | 'learning' | 'reflective'): StructuredMessage[] {
    return this.currentContext.recentMessages.filter(
      msg => msg.responseType?.type === type
    );
  }

  getMessagesRequiringMindmap(): StructuredMessage[] {
    return this.currentContext.recentMessages.filter(
      msg => msg.responseType?.requiresMindmap
    );
  }
} 